{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 14661550995293435084, "deps": [[40386456601120721, "percent_encoding", false, 5246120859346668611], [654232091421095663, "tauri_utils", false, 13003758921617556950], [1200537532907108615, "url<PERSON><PERSON>n", false, 676323353153747318], [2013030631243296465, "webview2_com", false, 6484000361824302609], [3150220818285335163, "url", false, 10192482888369876901], [3331586631144870129, "getrandom", false, 922718123510441438], [4143744114649553716, "raw_window_handle", false, 8871094038573090994], [4494683389616423722, "muda", false, 7462522351940758439], [4919829919303820331, "serialize_to_javascript", false, 14871317887498969924], [5986029879202738730, "log", false, 13360595710959020976], [8569119365930580996, "serde_json", false, 5582898300707703612], [9010263965687315507, "http", false, 4609389182342060280], [9689903380558560274, "serde", false, 16629909112705653095], [10229185211513642314, "mime", false, 4472229105638937915], [10806645703491011684, "thiserror", false, 12729569653178037635], [11989259058781683633, "dunce", false, 18087009893956605591], [12092653563678505622, "build_script_build", false, 18423637238081904029], [12304025191202589669, "tauri_runtime_wry", false, 6873621476454358765], [12393800526703971956, "tokio", false, 17471589730062437466], [12565293087094287914, "window_vibrancy", false, 2015097132996108150], [12943761728066819757, "tauri_runtime", false, 15465024999967887493], [12986574360607194341, "serde_repr", false, 649235668175806710], [13077543566650298139, "heck", false, 3833667618904260674], [13405681745520956630, "tauri_macros", false, 654372330463789177], [13625485746686963219, "anyhow", false, 13363454249419097510], [14585479307175734061, "windows", false, 12529223602217957790], [16928111194414003569, "dirs", false, 1609515387622353247], [17155886227862585100, "glob", false, 14311527598433297175]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-96dea6e899c6c007\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}