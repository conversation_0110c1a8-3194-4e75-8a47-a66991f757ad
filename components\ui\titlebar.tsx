"use client"

import React from "react"
import { Minus, X, Maximize2, Minimize2 } from "lucide-react"
import { Button } from "@/components/ui/button"

interface TitleBarProps {
  title?: string
  onMinimize?: () => void
  onMaximize?: () => void
  onClose?: () => void
  isMaximized?: boolean
  theme?: "light" | "dark"
}

export function TitleBar({
  title = "简单笔记",
  onMinimize,
  onMaximize,
  onClose,
  isMaximized = false,
  theme = "light"
}: TitleBarProps) {
  return (
    <div
      className={`
        flex items-center justify-between h-10 px-4 select-none relative
        ${theme === "dark"
          ? "bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 border-b border-gray-700/30"
          : "bg-gradient-to-r from-white via-gray-50 to-white border-b border-gray-200/30"
        }
        backdrop-blur-md shadow-sm
      `}
      data-tauri-drag-region
    >
      {/* 左侧：应用图标和标题 */}
      <div className="flex items-center gap-3 flex-1 min-w-0">
        <div className={`
          w-6 h-6 rounded-md flex items-center justify-center text-sm font-bold shadow-sm
          ${theme === "dark"
            ? "bg-gradient-to-br from-blue-500 via-purple-500 to-indigo-600 text-white"
            : "bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white"
          }
        `}>
          📝
        </div>
        <span className={`
          text-sm font-semibold truncate
          ${theme === "dark" ? "text-gray-100" : "text-gray-800"}
        `}>
          {title}
        </span>
      </div>

      {/* 右侧：窗口控制按钮 */}
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={onMinimize}
          className={`
            h-7 w-7 p-0 rounded-md transition-all duration-200 shadow-sm
            ${theme === "dark"
              ? "hover:bg-gray-700/60 text-gray-400 hover:text-gray-100 hover:shadow-md"
              : "hover:bg-gray-200/60 text-gray-500 hover:text-gray-800 hover:shadow-md"
            }
          `}
          title="最小化"
        >
          <Minus className="h-3.5 w-3.5" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={onMaximize}
          className={`
            h-7 w-7 p-0 rounded-md transition-all duration-200 shadow-sm
            ${theme === "dark"
              ? "hover:bg-gray-700/60 text-gray-400 hover:text-gray-100 hover:shadow-md"
              : "hover:bg-gray-200/60 text-gray-500 hover:text-gray-800 hover:shadow-md"
            }
          `}
          title={isMaximized ? "还原" : "最大化"}
        >
          {isMaximized ? (
            <Minimize2 className="h-3.5 w-3.5" />
          ) : (
            <Maximize2 className="h-3.5 w-3.5" />
          )}
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className={`
            h-7 w-7 p-0 rounded-md transition-all duration-200 shadow-sm
            hover:bg-red-500 hover:text-white hover:shadow-md
            ${theme === "dark" ? "text-gray-400" : "text-gray-500"}
          `}
          title="关闭"
        >
          <X className="h-3.5 w-3.5" />
        </Button>
      </div>
    </div>
  )
}
