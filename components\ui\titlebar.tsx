"use client"

import React from "react"
import { Minus, X, Maximize2, Minimize2 } from "lucide-react"
import { Button } from "@/components/ui/button"

interface TitleBarProps {
  title?: string
  onMinimize?: () => void
  onMaximize?: () => void
  onClose?: () => void
  isMaximized?: boolean
  theme?: "light" | "dark"
}

export function TitleBar({
  title = "简单笔记",
  onMinimize,
  onMaximize,
  onClose,
  isMaximized = false,
  theme = "light"
}: TitleBarProps) {
  return (
    <div
      className={`
        flex items-center justify-between h-8 px-3 select-none
        ${theme === "dark"
          ? "bg-gray-800 border-b border-gray-700"
          : "bg-gray-100 border-b border-gray-200"
        }
      `}
      data-tauri-drag-region
    >
      {/* 左侧：应用图标和标题 */}
      <div className="flex items-center gap-2 flex-1 min-w-0">
        <span className="text-sm">📝</span>
        <span className={`
          text-sm font-medium truncate
          ${theme === "dark" ? "text-gray-200" : "text-gray-700"}
        `}>
          {title}
        </span>
      </div>

      {/* 右侧：窗口控制按钮 */}
      <div className="flex items-center">
        <Button
          variant="ghost"
          size="sm"
          onClick={onMinimize}
          className={`
            h-6 w-6 p-0 rounded transition-colors
            ${theme === "dark"
              ? "hover:bg-gray-700 text-gray-400 hover:text-gray-200"
              : "hover:bg-gray-200 text-gray-500 hover:text-gray-700"
            }
          `}
          title="最小化"
        >
          <Minus className="h-3 w-3" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={onMaximize}
          className={`
            h-6 w-6 p-0 rounded transition-colors
            ${theme === "dark"
              ? "hover:bg-gray-700 text-gray-400 hover:text-gray-200"
              : "hover:bg-gray-200 text-gray-500 hover:text-gray-700"
            }
          `}
          title={isMaximized ? "还原" : "最大化"}
        >
          {isMaximized ? (
            <Minimize2 className="h-3 w-3" />
          ) : (
            <Maximize2 className="h-3 w-3" />
          )}
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className={`
            h-6 w-6 p-0 rounded transition-colors
            hover:bg-red-500 hover:text-white
            ${theme === "dark" ? "text-gray-400" : "text-gray-500"}
          `}
          title="关闭"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    </div>
  )
}
