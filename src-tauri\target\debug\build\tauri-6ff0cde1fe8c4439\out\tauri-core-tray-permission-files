["\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-6ff0cde1fe8c4439\\out\\permissions\\tray\\autogenerated\\commands\\get_by_id.toml", "\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-6ff0cde1fe8c4439\\out\\permissions\\tray\\autogenerated\\commands\\new.toml", "\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-6ff0cde1fe8c4439\\out\\permissions\\tray\\autogenerated\\commands\\remove_by_id.toml", "\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-6ff0cde1fe8c4439\\out\\permissions\\tray\\autogenerated\\commands\\set_icon.toml", "\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-6ff0cde1fe8c4439\\out\\permissions\\tray\\autogenerated\\commands\\set_icon_as_template.toml", "\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-6ff0cde1fe8c4439\\out\\permissions\\tray\\autogenerated\\commands\\set_menu.toml", "\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-6ff0cde1fe8c4439\\out\\permissions\\tray\\autogenerated\\commands\\set_show_menu_on_left_click.toml", "\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-6ff0cde1fe8c4439\\out\\permissions\\tray\\autogenerated\\commands\\set_temp_dir_path.toml", "\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-6ff0cde1fe8c4439\\out\\permissions\\tray\\autogenerated\\commands\\set_title.toml", "\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-6ff0cde1fe8c4439\\out\\permissions\\tray\\autogenerated\\commands\\set_tooltip.toml", "\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-6ff0cde1fe8c4439\\out\\permissions\\tray\\autogenerated\\commands\\set_visible.toml", "\\\\?\\E:\\studycode\\sky\\sky_react\\jdnotes\\src-tauri\\target\\debug\\build\\tauri-6ff0cde1fe8c4439\\out\\permissions\\tray\\autogenerated\\default.toml"]