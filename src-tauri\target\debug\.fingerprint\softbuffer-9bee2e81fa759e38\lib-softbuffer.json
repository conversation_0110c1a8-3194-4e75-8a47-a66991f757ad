{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 15657897354478470176, "path": 4764123382064906521, "deps": [[376837177317575824, "build_script_build", false, 902019838726820012], [4143744114649553716, "raw_window_handle", false, 8871094038573090994], [5986029879202738730, "log", false, 13360595710959020976], [10281541584571964250, "windows_sys", false, 6322733297633875155]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-9bee2e81fa759e38\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}