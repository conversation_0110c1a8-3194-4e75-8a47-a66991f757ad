{"rustc": 1842507548689473721, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 17110922245408071272, "deps": [[654232091421095663, "tauri_utils", false, 17126272240604541556], [2704937418414716471, "tauri_codegen", false, 4920562199531931454], [3060637413840920116, "proc_macro2", false, 12829056815806260006], [4974441333307933176, "syn", false, 6749378172925938269], [13077543566650298139, "heck", false, 3833667618904260674], [17990358020177143287, "quote", false, 7892140333893364853]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-c5682fc4fb6614ca\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}