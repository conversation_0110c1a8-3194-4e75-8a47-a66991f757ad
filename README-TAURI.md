# 简单笔记 - <PERSON><PERSON> 桌面应用

这是一个基于 Next.js + <PERSON><PERSON> 构建的桌面笔记应用，具有自定义标题栏和现代化的界面设计。

## 功能特性

- ✨ 自定义标题栏和窗口边框
- 🎨 支持明暗主题切换
- 📝 Markdown 语法支持
- 🔍 实时搜索功能
- 🏷️ 标签管理系统
- 💾 自动保存功能
- 🎯 代码高亮显示
- 📱 响应式设计

## 开发环境要求

- Node.js 18+
- Rust 1.70+
- Tauri CLI

## 安装依赖

```bash
# 安装 Node.js 依赖
npm install

# 安装 Tauri CLI（如果还没有安装）
npm install -g @tauri-apps/cli
```

## 开发模式

```bash
# 启动开发服务器
npm run tauri:dev
```

## 构建应用

```bash
# 构建生产版本
npm run tauri:build
```

构建完成后，可执行文件将位于 `src-tauri/target/release/` 目录中。

## 自定义标题栏功能

- **最小化**：点击 `-` 按钮
- **最大化/还原**：点击 `□` 按钮
- **关闭**：点击 `×` 按钮
- **拖拽移动**：点击标题栏区域拖拽窗口

## 快捷键

- `Ctrl + N`：新建笔记
- `Ctrl + F`：搜索笔记
- `Ctrl + S`：保存当前编辑
- `Esc`：取消编辑
- `Tab`：在编辑器中插入制表符

## 主题设计

应用采用现代化的设计风格：

- **明亮主题**：白色背景，灰色边框，蓝紫色渐变图标
- **暗黑主题**：深灰色背景，暗色边框，蓝紫色渐变图标
- **自适应边框**：窗口最大化时自动隐藏圆角和阴影
- **毛玻璃效果**：标题栏采用半透明毛玻璃效果

## 技术栈

- **前端**：Next.js 15, React 19, TypeScript
- **样式**：Tailwind CSS, Radix UI
- **桌面框架**：Tauri 2.0
- **图标**：Lucide React

## 项目结构

```
├── app/                    # Next.js 应用目录
├── components/            # React 组件
│   └── ui/               # UI 组件
│       └── titlebar.tsx  # 自定义标题栏
├── hooks/                # React Hooks
│   └── use-tauri-window.ts # Tauri 窗口控制
├── src-tauri/            # Tauri 后端
│   ├── src/              # Rust 源码
│   └── tauri.conf.json   # Tauri 配置
└── styles/               # 样式文件
```

## 注意事项

1. 确保已正确配置 Tauri 开发环境
2. 首次构建可能需要较长时间下载 Rust 依赖
3. 构建的应用大小约为 10-15MB
4. 支持 Windows、macOS 和 Linux 平台
