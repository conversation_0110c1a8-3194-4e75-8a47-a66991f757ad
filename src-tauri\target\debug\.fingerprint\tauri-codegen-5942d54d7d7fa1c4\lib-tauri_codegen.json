{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463************, "path": 8879837628796626640, "deps": [[654232091421095663, "tauri_utils", false, 17126272240604541556], [3060637413840920116, "proc_macro2", false, 12829056815806260006], [3150220818285335163, "url", false, 1050259682604575686], [4899080583175475170, "semver", false, 11884005311931207886], [4974441333307933176, "syn", false, 6749378172925938269], [7170110829644101142, "json_patch", false, 17997301177515447130], [7392050791754369441, "ico", false, 10682326940910309452], [8319709847752024821, "uuid", false, 3528261222286548766], [8569119365930580996, "serde_json", false, 18256112833044055150], [9556762810601084293, "brotli", false, 13038189564160369710], [9689903380558560274, "serde", false, 16869471455350232705], [9857275760291862238, "sha2", false, 10255520590411424512], [10806645703491011684, "thiserror", false, 12729569653178037635], [12687914511023397207, "png", false, 13034613678770539607], [13077212702700853852, "base64", false, 16702054904726379632], [15622660310229662834, "walkdir", false, 7545398721092262270], [17990358020177143287, "quote", false, 7892140333893364853]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-5942d54d7d7fa1c4\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}