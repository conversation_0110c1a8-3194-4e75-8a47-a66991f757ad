# 自定义标题栏测试指南

## 快速测试

### 开发模式测试
```bash
npm run tauri:dev
```

### 构建模式测试
```bash
npm run tauri:build
```

## 预期效果

### 标题栏显示
- ✅ 应该在窗口顶部看到一个灰色的标题栏
- ✅ 左侧显示 📝 图标和 "简单笔记" 标题
- ✅ 右侧显示三个窗口控制按钮：最小化、最大化、关闭

### 功能测试
1. **拖拽移动**：点击标题栏空白区域可以拖拽窗口
2. **最小化**：点击 `-` 按钮，窗口应该最小化到任务栏
3. **最大化/还原**：点击 `□` 按钮，窗口在最大化和还原之间切换
4. **关闭**：点击 `×` 按钮，应用关闭

### 主题适配
- 明亮主题：灰色标题栏，深色文字
- 暗黑主题：深灰色标题栏，浅色文字

## 故障排除

### 如果在 tauri dev 中看不到标题栏：
1. 确保 `decorations: false` 在 tauri.conf.json 中设置
2. 重启开发服务器
3. 尝试构建模式：`npm run tauri:build`

### 如果按钮不工作：
1. 检查控制台是否有 JavaScript 错误
2. 确保 @tauri-apps/api 已正确安装

### 如果无法拖拽窗口：
1. 确保标题栏有 `data-tauri-drag-region` 属性
2. 检查是否有其他元素阻挡了拖拽区域

## 配置说明

当前配置：
- 窗口大小：1200x800（最小 800x600）
- 无系统装饰：`decorations: false`
- 不透明：`transparent: false`
- 居中显示：`center: true`

这些设置确保自定义标题栏在开发和构建模式下都能正常工作。
