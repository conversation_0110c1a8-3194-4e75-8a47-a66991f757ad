{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[6186838405672744481, "build_script_build", false, 6621283002619310741]], "local": [{"RerunIfChanged": {"output": "debug\\build\\vswhom-sys-a4ad1f657f98f6fb\\output", "paths": ["build.rs", "ext/vswhom.cpp"]}}, {"RerunIfEnvChanged": {"var": "VCINSTALLDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "VSTEL_MSBuildProjectFullPath", "val": null}}, {"RerunIfEnvChanged": {"var": "VSCMD_ARG_VCVARS_SPECTRE", "val": null}}, {"RerunIfEnvChanged": {"var": "WindowsSdkDir", "val": null}}, {"RerunIfEnvChanged": {"var": "WindowsSDKVersion", "val": null}}, {"RerunIfEnvChanged": {"var": "LIB", "val": null}}, {"RerunIfEnvChanged": {"var": "INCLUDE", "val": null}}, {"RerunIfEnvChanged": {"var": "CXX_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CXX_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXX", "val": null}}, {"RerunIfEnvChanged": {"var": "CXX", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_AR", "val": null}}, {"RerunIfEnvChanged": {"var": "AR", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXSTDLIB_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXSTDLIB_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXSTDLIB", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXSTDLIB", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}