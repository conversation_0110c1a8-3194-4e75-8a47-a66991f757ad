{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 8943067380521412502, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 676323353153747318], [3150220818285335163, "url", false, 10192482888369876901], [3191507132440681679, "serde_untagged", false, 8363294130853665303], [4071963112282141418, "serde_with", false, 16859622446459044757], [4899080583175475170, "semver", false, 12760717825204468268], [5986029879202738730, "log", false, 13360595710959020976], [6606131838865521726, "ctor", false, 3502516644992933043], [7170110829644101142, "json_patch", false, 502560519496153278], [8319709847752024821, "uuid", false, 10614966501612569107], [8569119365930580996, "serde_json", false, 5582898300707703612], [9010263965687315507, "http", false, 4609389182342060280], [9451456094439810778, "regex", false, 17856527113523155721], [9556762810601084293, "brotli", false, 13038189564160369710], [9689903380558560274, "serde", false, 16629909112705653095], [10806645703491011684, "thiserror", false, 12729569653178037635], [11989259058781683633, "dunce", false, 18087009893956605591], [13625485746686963219, "anyhow", false, 13363454249419097510], [15609422047640926750, "toml", false, 14231897203879106052], [15622660310229662834, "walkdir", false, 17184090017219631149], [15932120279885307830, "memchr", false, 7563584842651218297], [17146114186171651583, "infer", false, 1402297252340479762], [17155886227862585100, "glob", false, 14311527598433297175], [17186037756130803222, "phf", false, 13656847138340893171]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-99d7520cdcb9ec78\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}