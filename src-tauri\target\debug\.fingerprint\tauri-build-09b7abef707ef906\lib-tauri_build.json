{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 14809081417461788049, "deps": [[654232091421095663, "tauri_utils", false, 17126272240604541556], [4899080583175475170, "semver", false, 11884005311931207886], [6913375703034175521, "schemars", false, 9689213738157718254], [7170110829644101142, "json_patch", false, 17997301177515447130], [8569119365930580996, "serde_json", false, 18256112833044055150], [9689903380558560274, "serde", false, 16869471455350232705], [12714016054753183456, "tauri_winres", false, 6514950457186026920], [13077543566650298139, "heck", false, 3833667618904260674], [13475171727366188400, "cargo_toml", false, 16237577300694025678], [13625485746686963219, "anyhow", false, 13363454249419097510], [15609422047640926750, "toml", false, 293773000198844333], [15622660310229662834, "walkdir", false, 7545398721092262270], [16928111194414003569, "dirs", false, 1609515387622353247], [17155886227862585100, "glob", false, 14311527598433297175]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-09b7abef707ef906\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}