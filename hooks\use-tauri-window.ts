"use client"

import { useState, useEffect } from "react"

// 检查是否在 Tauri 环境中
const isTauri = typeof window !== "undefined" && (window as any).__TAURI__

export function useTauriWindow() {
  const [isMaximized, setIsMaximized] = useState(false)

  useEffect(() => {
    if (!isTauri) return

    // 监听窗口状态变化
    const setupWindowListeners = async () => {
      try {
        const { appWindow } = await import("@tauri-apps/api/window")
        
        // 监听窗口最大化状态变化
        const unlistenResize = await appWindow.onResized(() => {
          appWindow.isMaximized().then(setIsMaximized)
        })

        // 初始化状态
        appWindow.isMaximized().then(setIsMaximized)

        return () => {
          unlistenResize()
        }
      } catch (error) {
        console.warn("Failed to setup window listeners:", error)
      }
    }

    setupWindowListeners()
  }, [])

  const minimize = async () => {
    if (!isTauri) return
    try {
      const { appWindow } = await import("@tauri-apps/api/window")
      await appWindow.minimize()
    } catch (error) {
      console.error("Failed to minimize window:", error)
    }
  }

  const maximize = async () => {
    if (!isTauri) return
    try {
      const { appWindow } = await import("@tauri-apps/api/window")
      if (isMaximized) {
        await appWindow.unmaximize()
      } else {
        await appWindow.maximize()
      }
    } catch (error) {
      console.error("Failed to maximize/unmaximize window:", error)
    }
  }

  const close = async () => {
    if (!isTauri) return
    try {
      const { appWindow } = await import("@tauri-apps/api/window")
      await appWindow.close()
    } catch (error) {
      console.error("Failed to close window:", error)
    }
  }

  return {
    isMaximized,
    minimize,
    maximize,
    close,
    isTauri
  }
}
