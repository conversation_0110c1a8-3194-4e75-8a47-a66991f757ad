{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 9831540453462500854, "deps": [[376837177317575824, "softbuffer", false, 3928454938716425897], [654232091421095663, "tauri_utils", false, 13003758921617556950], [2013030631243296465, "webview2_com", false, 6484000361824302609], [3150220818285335163, "url", false, 10192482888369876901], [3722963349756955755, "once_cell", false, 15951163895981159829], [4143744114649553716, "raw_window_handle", false, 8871094038573090994], [5986029879202738730, "log", false, 13360595710959020976], [8826339825490770380, "tao", false, 17876278652967208482], [9010263965687315507, "http", false, 4609389182342060280], [9141053277961803901, "wry", false, 5442018165130161284], [12304025191202589669, "build_script_build", false, 5355976548200665731], [12943761728066819757, "tauri_runtime", false, 15465024999967887493], [14585479307175734061, "windows", false, 12529223602217957790]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-997842b1422f34da\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}