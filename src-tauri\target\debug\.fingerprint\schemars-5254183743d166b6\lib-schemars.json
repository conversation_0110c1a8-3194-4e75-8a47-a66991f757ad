{"rustc": 1842507548689473721, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2225463790103693989, "path": 14627915132391153963, "deps": [[3150220818285335163, "url", false, 1050259682604575686], [6913375703034175521, "build_script_build", false, 1062029339344368048], [8319709847752024821, "uuid1", false, 3528261222286548766], [8569119365930580996, "serde_json", false, 18256112833044055150], [9122563107207267705, "dyn_clone", false, 13336709506101428405], [9689903380558560274, "serde", false, 16869471455350232705], [14923790796823607459, "indexmap", false, 12597395478618865415], [16071897500792579091, "schemars_derive", false, 495169833273072815]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-5254183743d166b6\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}